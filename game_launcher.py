#!/usr/bin/env python3
"""
Tower Defense Game Launcher
Foundation system for choosing configurations and automatic level generation
"""

import os
import sys
import json
import pygame
from typing import List, Dict, Any, Optional
from datetime import datetime
import time


class GameLauncher:
    """Main launcher for the tower defense game with configuration selection and adaptive level generation"""

    def __init__(self):
        """Initialize the game launcher"""
        pygame.init()

        # Screen setup
        self.SCREEN_WIDTH = 1000
        self.SCREEN_HEIGHT = 800
        self.screen = pygame.display.set_mode(
            (self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("Tower Defense - Game Launcher")

        # Define color scheme (modern, clean)
        self.BACKGROUND = (32, 34, 40)      # Dark modern background
        self.CARD_BG = (48, 52, 64)         # Card background
        self.HOVER_BG = (56, 60, 74)        # Hover background
        self.SELECTED_BG = (64, 68, 84)     # Selected background
        self.TEXT_PRIMARY = (240, 242, 247)  # Primary text
        self.TEXT_SECONDARY = (155, 164, 181)  # Secondary text
        self.ACCENT_BLUE = (59, 130, 246)   # Primary action color
        self.ACCENT_GREEN = (34, 197, 94)   # Success color
        self.ACCENT_ORANGE = (251, 146, 60)  # Warning color
        self.BORDER_LIGHT = (75, 85, 99)    # Light border
        self.BORDER_DARK = (31, 41, 55)     # Dark border

        # Visual feedback state tracking
        self.pressed_button = None  # Track which button is currently pressed
        self.pressed_card = None    # Track which card is currently pressed
        self.button_press_time = 0  # Time when button was pressed
        self.card_press_time = 0    # Time when card was pressed

        # Game loop settings
        self.clock = pygame.time.Clock()
        self.FPS = 60

        # Modern, elegant fonts
        self.title_font = pygame.font.Font(None, 42)
        self.subtitle_font = pygame.font.Font(None, 28)
        self.menu_font = pygame.font.Font(None, 24)
        self.info_font = pygame.font.Font(None, 20)
        self.small_font = pygame.font.Font(None, 16)

        # Initialize systems needed for configuration loading
        from game_systems.global_upgrade_system import GlobalUpgradeSystem
        from game_systems.global_upgrade_ui import GlobalUpgradeUI
        from game_systems.variant_selection_ui import VariantSelectionUI
        from game_systems.level_variant_generator import LevelVariantGenerator

        self.global_upgrade_system = GlobalUpgradeSystem()
        self.global_upgrade_ui = GlobalUpgradeUI(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.global_upgrade_system)
        self.variant_ui = VariantSelectionUI(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.variant_generator = LevelVariantGenerator()

        # State
        self.running = True
        self.selected_config = None
        self.configs = []  # Will hold base level configs only
        self.variants = {}  # Will hold variants organized by base level
        self.scroll_offset = 0
        self.max_scroll = 0
        self.current_view = "main"  # "main", "variants", or "level_options"
        self.selected_base_level = None  # Which base level's variants we're viewing
        self.level_options_config = None  # Config being shown in level options view

        # UI state
        self.show_generation_status = False
        self.generation_message = ""
        self.show_performance_panel = False
        self.show_upgrade_menu = False
        self.show_variant_selector = False
        self.pending_variant_creation = None

        # Load available configurations
        self.load_configurations()

        # Level preview system
        self.show_level_preview = False
        self.preview_config = None
        self.preview_scroll_offset = 0
        self.preview_max_scroll = 0

        # Load performance statistics
        self.load_performance_stats()

    def load_configurations(self):
        """Load and organize configurations from organized folder structure"""
        base_config_dir = "config/base"
        variants_config_dir = "config/variants"

        self.configs = []        # Base level configs only
        self.variants = {}       # Variants organized by base level
        all_configs = []         # Temporary list for processing

        # Load base configs from config/base/
        if os.path.exists(base_config_dir):
            for filename in os.listdir(base_config_dir):
                if filename.endswith('.json'):
                    config_path = os.path.join(base_config_dir, filename)
                    try:
                        with open(config_path, 'r') as f:
                            config_data = json.load(f)

                        # Determine config type
                        is_adaptive = '_adaptive_metadata' in config_data
                        is_variant = '_variant_metadata' in config_data

                        # Extract metadata for display
                        config_info = {
                            'filename': filename,
                            'path': config_path,
                            'name': self.get_config_display_name(filename, config_data),
                            'difficulty': self.get_config_difficulty(config_data, filename),
                            'description': self.get_config_description(filename, config_data),
                            'is_adaptive': is_adaptive,
                            'is_variant': is_variant,
                            'variant_completed': False,
                            'variant_reward_multiplier': 1.0,
                            'creation_time': self.get_config_creation_time(config_path, config_data),
                            'level_metadata': self.get_config_level_metadata(config_data, filename),
                            'config_data': config_data
                        }
                        all_configs.append(config_info)

                    except Exception as e:
                        print(f"Error loading base config {filename}: {e}")

        # Load variant configs from config/variants/
        if os.path.exists(variants_config_dir):
            for filename in os.listdir(variants_config_dir):
                if filename.endswith('.json'):
                    config_path = os.path.join(variants_config_dir, filename)
                    try:
                        with open(config_path, 'r') as f:
                            config_data = json.load(f)

                        # Determine config type
                        is_adaptive = '_adaptive_metadata' in config_data
                        is_variant = '_variant_metadata' in config_data

                        # For variants, get completion status
                        variant_completed = False
                        variant_reward_multiplier = 1.0
                        if is_variant and '_variant_metadata' in config_data:
                            metadata = config_data['_variant_metadata']
                            variant_id = metadata.get('variant_id', filename)
                            variant_completed = self.variant_generator._is_variant_completed(
                                variant_id)
                            variant_reward_multiplier = metadata.get(
                                'reward_multiplier', 1.0)

                        # Extract metadata for display
                        config_info = {
                            'filename': filename,
                            'path': config_path,
                            'name': self.get_config_display_name(filename, config_data),
                            'difficulty': self.get_config_difficulty(config_data, filename),
                            'description': self.get_config_description(filename, config_data),
                            'is_adaptive': is_adaptive,
                            'is_variant': is_variant,
                            'variant_completed': variant_completed,
                            'variant_reward_multiplier': variant_reward_multiplier,
                            'creation_time': self.get_config_creation_time(config_path, config_data),
                            'level_metadata': self.get_config_level_metadata(config_data, filename),
                            'config_data': config_data
                        }
                        all_configs.append(config_info)

                    except Exception as e:
                        print(f"Error loading variant config {filename}: {e}")

        # Also check main config directory for adaptive/generated configs
        main_config_dir = "config"
        excluded_files = {
            'static_tower_config.json', 'static_balance_config.json', 'game_config.py',
            'README.md', 'tower_defense_game.json.backup', 'variant_completion.json'
        }

        if os.path.exists(main_config_dir):
            for filename in os.listdir(main_config_dir):
                if filename.endswith('.json') and filename not in excluded_files and not filename.endswith('.backup'):
                    config_path = os.path.join(main_config_dir, filename)
                    try:
                        with open(config_path, 'r') as f:
                            config_data = json.load(f)

                        # Only include numbered adaptive configs (1.json, 2.json, etc.)
                        if filename.replace('.json', '').isdigit():
                            is_adaptive = '_adaptive_metadata' in config_data

                            config_info = {
                                'filename': filename,
                                'path': config_path,
                                'name': self.get_config_display_name(filename, config_data),
                                'difficulty': self.get_config_difficulty(config_data, filename),
                                'description': self.get_config_description(filename, config_data),
                                'is_adaptive': is_adaptive,
                                'is_variant': False,
                                'variant_completed': False,
                                'variant_reward_multiplier': 1.0,
                                'creation_time': self.get_config_creation_time(config_path, config_data),
                                'level_metadata': self.get_config_level_metadata(config_data, filename),
                                'config_data': config_data
                            }
                            all_configs.append(config_info)

                    except Exception as e:
                        print(f"Error loading adaptive config {filename}: {e}")

        # Organize configs by type
        for config in all_configs:
            if config['is_variant']:
                # This is a variant - organize by base level
                if '_variant_metadata' in config['config_data']:
                    metadata = config['config_data']['_variant_metadata']
                    base_level = metadata.get('base_level', 'unknown')
                else:
                    # Infer base level from filename for variants without metadata
                    filename = config['filename']
                    if filename.startswith('tower_defense_game_'):
                        base_level = 'tower_defense_game'
                    elif filename.startswith('test_config_'):
                        base_level = 'test_config'
                    else:
                        base_level = 'unknown'

                if base_level not in self.variants:
                    self.variants[base_level] = []
                self.variants[base_level].append(config)

            else:
                # This is a base level config or adaptive config
                self.configs.append(config)

        # Sort base configs by priority
        def sort_base_configs(config):
            filename = config['filename']
            # Priority 1: Numbered configs (1.json, 2.json, etc.)
            if filename.replace('.json', '').isdigit():
                return (0, int(filename.replace('.json', '')))
            # Priority 2: Named base configs
            else:
                return (1, config['creation_time'])

        self.configs.sort(key=sort_base_configs, reverse=True)

        # Sort variants within each base level
        for base_level in self.variants:
            self.variants[base_level].sort(
                key=lambda x: x['creation_time'], reverse=True)

        # Calculate scroll limits
        self.update_scroll_limits()

    def update_scroll_limits(self):
        """Update scroll limits based on current view"""
        cards_per_row = 3
        rows_visible = 4
        cards_visible = cards_per_row * rows_visible

        if self.current_view == "main":
            total_configs = len(self.configs)
        else:  # variants view
            base_level = self.selected_base_level
            total_configs = len(self.variants.get(
                base_level, [])) + 1  # +1 for back button

        if total_configs > cards_visible:
            self.max_scroll = total_configs - cards_visible
        else:
            self.max_scroll = 0

    def load_performance_stats(self):
        """Load and analyze recent performance statistics"""
        try:
            from ai.adaptive_config_generator import load_all_recent_performances

            # Load recent performances
            performances = load_all_recent_performances()

            if not performances:
                self.recent_performance_stats = None
                return

            # Calculate aggregate statistics
            scores = [p.score for p in performances]
            avg_score = sum(scores) / len(scores)
            win_rate = sum(1 for p in performances if p.win_flag) / \
                len(performances) * 100

            # Get performance trend (improving/declining)
            trend = "stable"
            if len(scores) >= 3:
                recent_avg = sum(scores[:2]) / 2  # Last 2 games
                older_avg = sum(scores[2:]) / len(scores[2:])  # Older games
                if recent_avg > older_avg + 10:
                    trend = "improving"
                elif recent_avg < older_avg - 10:
                    trend = "declining"

            # Create performance history for display
            performance_history = []
            for i, perf in enumerate(performances):
                performance_history.append({
                    'game_number': i + 1,
                    'score': perf.score,
                    'won': perf.win_flag,
                    'lives_remaining_pct': (perf.lives_remaining / perf.starting_lives) * 100,
                    'towers_used': len([t for t, c in perf.towers_built.items() if c > 0])
                })

            self.recent_performance_stats = {
                'games_count': len(performances),
                'average_score': avg_score,
                'win_rate': win_rate,
                'trend': trend,
                'performance_history': performance_history,
                'latest_score': scores[0] if scores else 0
            }

        except Exception as e:
            print(f"Error loading performance stats: {e}")
            self.recent_performance_stats = None

    def get_config_display_name(self, filename: str, config_data: Dict) -> str:
        """Generate a user-friendly display name for the configuration"""

        # Check for AI-generated level name first
        if 'level_name' in config_data:
            return config_data['level_name']

        # Check for variant metadata name
        if '_variant_metadata' in config_data and 'variant_name' in config_data['_variant_metadata']:
            return config_data['_variant_metadata']['variant_name']

        # Check for level metadata name
        if 'level_metadata' in config_data and 'name' in config_data['level_metadata']:
            return config_data['level_metadata']['name']

        # Check if it's a numbered adaptive config
        if filename.replace('.json', '').isdigit():
            number = filename.replace('.json', '')
            if '_adaptive_metadata' in config_data:
                # Handle both old and new metadata structures
                if 'previous_performance' in config_data['_adaptive_metadata']:
                    score = config_data['_adaptive_metadata']['previous_performance'].get(
                        'score', 0)
                elif 'recent_performance_summary' in config_data['_adaptive_metadata']:
                    score = config_data['_adaptive_metadata']['recent_performance_summary'].get(
                        'average_score', 0)
                else:
                    score = 0
                return f"Level {number} (Adaptive - {score:.1f}% performance)"
            else:
                return f"Level {number}"

        # Handle named configs
        if filename == 'tower_defense_game.json':
            name = "Tower Defense Game"
        elif filename == 'test_config.json':
            name = "Test Config"
        else:
            name = filename.replace('.json', '').replace('_', ' ').title()

        # Add descriptive suffixes
        if 'adaptive' in filename.lower():
            return f"{name} (AI-Generated)"
        elif 'demo' in filename.lower():
            return f"{name} (Demo)"
        elif 'nightmare' in filename.lower():
            return f"{name} (Nightmare)"
        elif 'hard' in filename.lower():
            return f"{name} (Hard)"
        elif 'normal' in filename.lower():
            return f"{name} (Normal)"
        elif 'tutorial' in filename.lower():
            return f"{name} (Tutorial)"
        elif 'casual' in filename.lower():
            return f"{name} (Casual)"

        return name

    def get_config_difficulty(self, config_data: Dict, filename: str = "") -> int:
        """Extract difficulty rating from config data"""
        if '_generation_metadata' in config_data:
            diff = config_data['_generation_metadata'].get('difficulty', 50)
            return int(diff) if isinstance(diff, (int, float)) else 50
        elif '_adaptive_metadata' in config_data:
            diff = config_data['_adaptive_metadata']['ai_adjustments']['difficulty_adjustment'].get(
                'new_difficulty', 50)
            return int(diff) if isinstance(diff, (int, float)) else 50
        elif 'calculated_difficulty' in config_data:
            # Check if it's a dict with 'score' field (like tower_defense_game.json)
            calc_diff = config_data.get('calculated_difficulty')
            if isinstance(calc_diff, dict):
                diff = calc_diff.get('score', 50)
            else:
                diff = calc_diff
            return int(diff) if isinstance(diff, (int, float)) else 50
        elif 'game_config' in config_data and 'difficulty' in config_data['game_config']:
            # Check for difficulty in game_config section
            game_config = config_data['game_config']
            difficulty_value = game_config.get('difficulty')

            # Convert difficulty level names to scores
            if isinstance(difficulty_value, str):
                difficulty_map = {
                    'test': 1,
                    'tutorial': 10,
                    'easy': 20,
                    'casual': 25,
                    'normal': 50,
                    'medium': 60,
                    'hard': 70,
                    'very_hard': 85,
                    'nightmare': 95,
                    'impossible': 100
                }
                return difficulty_map.get(difficulty_value.lower(), 50)
            elif isinstance(difficulty_value, (int, float)):
                return int(difficulty_value)
        elif 'difficulty' in config_data:
            # Check for difficulty at root level
            diff_data = config_data.get('difficulty')
            if isinstance(diff_data, dict):
                # Check for score in difficulty object
                diff = diff_data.get('score', diff_data.get('level', 50))
            else:
                diff = diff_data

            # Convert difficulty level names to scores
            if isinstance(diff, str):
                difficulty_map = {
                    'test': 1,
                    'tutorial': 10,
                    'easy': 20,
                    'casual': 25,
                    'normal': 50,
                    'medium': 60,
                    'hard': 70,
                    'very_hard': 85,
                    'nightmare': 95,
                    'impossible': 100
                }
                return difficulty_map.get(diff.lower(), 50)
            elif isinstance(diff, (int, float)):
                return int(diff)
        else:
            # Guess based on filename
            filename_lower = filename.lower()
            if 'nightmare' in filename_lower:
                return 90
            elif 'hard' in filename_lower:
                return 70
            elif 'normal' in filename_lower:
                return 50
            elif 'tutorial' in filename_lower or 'easy' in filename_lower:
                return 30
            elif 'casual' in filename_lower:
                return 20
            elif 'test' in filename_lower:
                return 1
            else:
                return 50

        # Default fallback
        return 50

    def get_config_description(self, filename: str, config_data: Dict) -> str:
        """Generate a description of the configuration"""
        if '_variant_metadata' in config_data:
            # Variant config - show modifiers and multipliers
            metadata = config_data['_variant_metadata']
            base_level = metadata.get('base_level', 'Unknown')
            modifiers = metadata.get('modifiers', [])
            difficulty_mult = metadata.get('difficulty_multiplier', 1.0)
            reward_mult = metadata.get('reward_multiplier', 1.0)

            if modifiers:
                modifier_names = [mod.get('name', mod.get(
                    'id', 'Unknown')) for mod in modifiers]
                if len(modifier_names) == 1:
                    mod_desc = modifier_names[0]
                elif len(modifier_names) == 2:
                    mod_desc = f"{modifier_names[0]} + {modifier_names[1]}"
                else:
                    mod_desc = f"{len(modifier_names)} modifiers"
                return f"Variant of {base_level}: {mod_desc} ({difficulty_mult:.1f}x difficulty, {reward_mult:.1f}x reward)"
            else:
                return f"Variant of {base_level} with custom modifications"

        elif '_adaptive_metadata' in config_data:
            # Adaptive config - show what it was adapted for
            # Handle multiple metadata structures
            score = 0

            # New format: multi_game_context
            if 'multi_game_context' in config_data['_adaptive_metadata']:
                score = config_data['_adaptive_metadata']['multi_game_context'].get(
                    'avg_score', 0)
            # Old format: previous_performance
            elif 'previous_performance' in config_data['_adaptive_metadata']:
                performance = config_data['_adaptive_metadata']['previous_performance']
                score = performance.get('score', 0)
            # Alternative old format: recent_performance_summary
            elif 'recent_performance_summary' in config_data['_adaptive_metadata']:
                performance = config_data['_adaptive_metadata']['recent_performance_summary']
                score = performance.get('average_score', 0)

            reasoning = config_data['_adaptive_metadata']['ai_adjustments'].get(
                'reasoning', '')

            # Truncate reasoning for display
            if len(reasoning) > 100:
                reasoning = reasoning[:100] + "..."

            return f"Generated for {score:.1f}% performance. {reasoning}"

        elif '_generation_metadata' in config_data:
            # Generated config
            meta = config_data['_generation_metadata']
            return f"Difficulty {meta.get('difficulty', 'Unknown')} - Procedurally generated level"

        else:
            # Manual/preset config
            if 'nightmare' in filename.lower():
                return "Ultimate challenge for expert players"
            elif 'hard' in filename.lower():
                return "Challenging gameplay for experienced players"
            elif 'normal' in filename.lower():
                return "Balanced gameplay for most players"
            elif 'tutorial' in filename.lower():
                return "Learning experience for new players"
            elif 'casual' in filename.lower():
                return "Relaxed gameplay for casual sessions"
            elif 'demo' in filename.lower():
                return "Demonstration of game features"
            else:
                return "Custom configuration"

    def get_config_creation_time(self, config_path: str, config_data: Dict) -> float:
        """Get the creation time of the configuration"""
        # Try to get timestamp from adaptive metadata
        if '_adaptive_metadata' in config_data:
            timestamp_str = config_data['_adaptive_metadata'].get(
                'generation_timestamp')
            if timestamp_str:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(
                        timestamp_str.replace('Z', '+00:00'))
                    return dt.timestamp()
                except:
                    pass

        # Fall back to file modification time
        try:
            return os.path.getmtime(config_path)
        except:
            return 0

    def get_config_level_metadata(self, config_data: Dict, filename: str) -> Dict:
        """Extract level metadata from config data"""
        # Check if config has level_metadata section
        if 'level_metadata' in config_data:
            return config_data['level_metadata']

        # Generate metadata from config info
        difficulty_score = self.get_config_difficulty(config_data, filename)

        # Calculate victory points using the same formula as GlobalUpgradeSystem
        if difficulty_score <= 1:
            victory_points = 1
        elif difficulty_score >= 100:
            victory_points = 15
        else:
            victory_points = 1 + int((difficulty_score - 1) * (14 / 99))

        # Generate difficulty name
        difficulty_name = "Unknown"
        if difficulty_score <= 10:
            difficulty_name = "Very Easy"
        elif difficulty_score <= 30:
            difficulty_name = "Easy"
        elif difficulty_score <= 50:
            difficulty_name = "Normal"
        elif difficulty_score <= 70:
            difficulty_name = "Hard"
        elif difficulty_score <= 85:
            difficulty_name = "Very Hard"
        else:
            difficulty_name = "Nightmare"

        # Generate default metadata
        return {
            "name": self.get_config_display_name(filename, config_data),
            "description": f"A {difficulty_name.lower()} challenge that will test your strategic skills. Prepare your defenses and adapt your tactics to overcome the incoming waves of enemies.",
            "difficulty_rating": difficulty_name,
            "estimated_duration": "30-45 minutes",
            "recommended_for": "all players",
            "special_features": [
                f"Difficulty rating: {difficulty_score}/100",
                "Dynamic enemy scaling",
                "Strategic tower placement required"
            ],
            "victory_rewards": {
                "victory_points": victory_points,
                "victory_points_description": f"Earn {victory_points} Victory Points for completing this level",
                "unlock_requirements": [],
                "completion_bonuses": [
                    "Tower upgrade points",
                    "Strategic experience",
                    "Achievement progress"
                ]
            },
            "tips": [
                "Study enemy types and their weaknesses",
                "Balance offense and economy",
                "Adapt your strategy as waves progress",
                "Use terrain to your advantage"
            ]
        }

    def handle_events(self):
        """Handle launcher events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    if self.show_level_preview:
                        self.preview_scroll_offset = max(
                            0, self.preview_scroll_offset - 1)
                    elif self.selected_config is None:
                        self.selected_config = 0
                    else:
                        self.selected_config = max(0, self.selected_config - 1)
                    self.update_scroll()
                elif event.key == pygame.K_DOWN:
                    if self.show_level_preview:
                        self.preview_scroll_offset = min(
                            self.preview_max_scroll, self.preview_scroll_offset + 1)
                    elif self.selected_config is None:
                        self.selected_config = 0
                    else:
                        current_configs = self.get_current_config_list()
                        self.selected_config = min(
                            len(current_configs) - 1, self.selected_config + 1)
                    self.update_scroll()
                elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:
                    if self.show_level_preview and self.preview_config:
                        self.launch_game(self.preview_config)
                    elif self.current_view == "level_options" and self.level_options_config:
                        # In level options view, enter/space triggers "Play Original"
                        self.show_level_preview = True
                        self.preview_config = self.level_options_config
                        self.preview_scroll_offset = 0
                    else:
                        current_configs = self.get_current_config_list()
                        if self.selected_config is not None and 0 <= self.selected_config < len(current_configs):
                            config = current_configs[self.selected_config]
                            if self.current_view == "main":
                                # Main menu card selected - open level options
                                self.navigate_to_level_options(config)
                            else:
                                # Variant in variants view - show preview
                                self.show_level_preview = True
                                self.preview_config = config
                                self.preview_scroll_offset = 0
                elif event.key == pygame.K_ESCAPE:
                    if self.show_variant_selector:
                        self.show_variant_selector = False
                        self.variant_ui.close()
                    elif self.show_level_preview:
                        self.show_level_preview = False
                        self.preview_config = None
                    elif self.show_upgrade_menu:
                        self.show_upgrade_menu = False
                    elif self.current_view == "level_options":
                        # Go back to main view from level options
                        self.navigate_to_main()
                    elif self.current_view == "variants":
                        # Go back to main view from variants
                        self.navigate_to_main()
                    else:
                        self.running = False
                elif event.key == pygame.K_g:
                    if not self.show_level_preview and not self.show_upgrade_menu:
                        # Generate new adaptive config from recent games (multi-game analysis)
                        self.generate_multi_game_adaptive_config()
                elif event.key == pygame.K_p:
                    if not self.show_level_preview and not self.show_upgrade_menu and not self.show_variant_selector:
                        # Toggle performance panel
                        self.show_performance_panel = not self.show_performance_panel
                elif event.key == pygame.K_v:
                    if not self.show_level_preview and not self.show_upgrade_menu and not self.show_variant_selector:
                        # Open variant selector for selected level
                        if self.selected_config is not None and 0 <= self.selected_config < len(self.configs):
                            config_info = self.configs[self.selected_config]
                            self.show_variant_selector = True
                            self.variant_ui.open_for_level(config_info['path'])

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    if self.show_variant_selector:
                        action = self.variant_ui.handle_click(event.pos)
                        if action == "create_variant":
                            self.handle_variant_creation()
                        elif action == "close":
                            self.show_variant_selector = False
                    elif self.show_level_preview:
                        self.handle_preview_click(event.pos)
                    elif self.show_upgrade_menu:
                        self.global_upgrade_ui.handle_click(event.pos)
                    else:
                        self.handle_mouse_click(event.pos)
                elif event.button == 4:  # Mouse wheel up
                    if self.show_variant_selector:
                        self.variant_ui.handle_scroll(-1)
                    elif self.show_level_preview:
                        self.preview_scroll_offset = max(
                            0, self.preview_scroll_offset - 1)
                    elif self.show_upgrade_menu:
                        self.global_upgrade_ui.handle_scroll(-1)
                    else:
                        self.scroll_offset = max(0, self.scroll_offset - 1)
                elif event.button == 5:  # Mouse wheel down
                    if self.show_variant_selector:
                        self.variant_ui.handle_scroll(1)
                    elif self.show_level_preview:
                        self.preview_scroll_offset = min(
                            self.preview_max_scroll, self.preview_scroll_offset + 1)
                    elif self.show_upgrade_menu:
                        self.global_upgrade_ui.handle_scroll(1)
                    else:
                        self.scroll_offset = min(
                            self.max_scroll, self.scroll_offset + 1)

            elif event.type == pygame.MOUSEWHEEL:
                if self.show_variant_selector:
                    self.variant_ui.handle_scroll(event.y)
                elif self.show_level_preview:
                    self.preview_scroll_offset = max(
                        0, min(self.preview_max_scroll, self.preview_scroll_offset - event.y))
                elif self.show_upgrade_menu:
                    self.global_upgrade_ui.handle_scroll(event.y)
                else:
                    self.scroll_offset = max(
                        0, min(self.max_scroll, self.scroll_offset - event.y))

    def handle_mouse_click(self, pos):
        """Handle mouse clicks on the hierarchical UI"""
        x, y = pos
        import time

        # Check if status message is showing and dismiss it
        if self.show_generation_status and self.generation_message:
            print("DEBUG: Dismissing status message")
            self.show_generation_status = False
            self.generation_message = ""
            return

        # Check button clicks first (should work in all views since buttons are always visible)
        if hasattr(self, 'button_coords'):
            # Generate button
            if 'generate' in self.button_coords:
                btn_x, btn_y, btn_w, btn_h = self.button_coords['generate']
                if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                    self.pressed_button = 'generate'
                    self.button_press_time = time.time()

                    self.generation_message = "🔄 Generating new level..."
                    self.show_generation_status = True

                    self.generate_multi_game_adaptive_config()
                    return

            # Stats button
            if 'stats' in self.button_coords:
                btn_x, btn_y, btn_w, btn_h = self.button_coords['stats']
                if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                    self.pressed_button = 'stats'
                    self.button_press_time = time.time()

                    self.show_performance_panel = not self.show_performance_panel
                    return

            # Upgrade button
            if 'upgrades' in self.button_coords:
                btn_x, btn_y, btn_w, btn_h = self.button_coords['upgrades']
                if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                    self.pressed_button = 'upgrades'
                    self.button_press_time = time.time()

                    self.show_upgrade_menu = not self.show_upgrade_menu
                    if self.show_upgrade_menu:
                        self.global_upgrade_ui.open_menu()
                    return

            # Variant button (if enabled)
            if 'variants' in self.button_coords:
                btn_x, btn_y, btn_w, btn_h = self.button_coords['variants']
                if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                    self.pressed_button = 'variants'
                    self.button_press_time = time.time()
                    self.handle_variant_creation()
                    return

        # Handle navigation buttons for different views
        if self.current_view == "variants":
            # Check for back button click in variants view
            back_btn_x, back_btn_y = 50, 50
            back_btn_width, back_btn_height = 100, 35
            if back_btn_x <= x <= back_btn_x + back_btn_width and back_btn_y <= y <= back_btn_y + back_btn_height:
                self.navigate_to_main()
                return

        elif self.current_view == "level_options":
            # Handle level options view clicks
            self.handle_level_options_click(pos)
            return

        # Check card clicks (this should happen for main view and variants view)
        current_configs = self.get_current_config_list()
        if current_configs:
            card_width = 250
            card_height = 120
            padding = 20
            margin = 40
            header_height = 100
            content_y = header_height + 20

            # In variants view, leave space for back button
            if self.current_view == "variants":
                content_y += 50

            # Calculate grid layout
            cards_per_row = (self.SCREEN_WIDTH - 2 *
                             margin) // (card_width + padding)

            # Check if click is in the card area
            if y >= content_y:
                # Calculate which card was clicked
                relative_x = x - margin
                relative_y = y - content_y

                if relative_x >= 0 and relative_y >= 0:
                    col = relative_x // (card_width + padding)
                    row = relative_y // (card_height + padding)

                    # Check if click is within a card boundary
                    card_x = col * (card_width + padding)
                    card_y = row * (card_height + padding)

                    if (card_x <= relative_x <= card_x + card_width and
                            card_y <= relative_y <= card_y + card_height):

                        clicked_index = row * cards_per_row + col + self.scroll_offset

                        if 0 <= clicked_index < len(current_configs):
                            config = current_configs[clicked_index]

                            # Track card press for visual feedback
                            self.pressed_card = clicked_index
                            self.card_press_time = time.time()

                            # Check if it's a play button click (only in variants view)
                            if self.current_view == "variants":
                                play_btn_x = card_x + card_width - 60
                                play_btn_y = card_y + card_height - 35

                                if (play_btn_x <= relative_x <= play_btn_x + 45 and
                                        play_btn_y <= relative_y <= play_btn_y + 25):
                                    # Play button clicked - show preview
                                    self.show_level_preview = True
                                    self.preview_config = config
                                    self.preview_scroll_offset = 0
                                    return

                            # Card clicked (not play button)
                            if self.current_view == "main":
                                # Main menu card clicked - open level options
                                self.navigate_to_level_options(config)
                            elif self.current_view == "variants":
                                # Variant card clicked (not play button) - show preview directly
                                self.show_level_preview = True
                                self.preview_config = config
                                self.preview_scroll_offset = 0
                            else:
                                # Other views - just select
                                self.selected_config = clicked_index

    def handle_level_options_click(self, pos):
        """Handle clicks in the level options view"""
        x, y = pos
        if not self.level_options_config:
            return

        # Back button
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 35
        if back_btn_x <= x <= back_btn_x + back_btn_width and back_btn_y <= y <= back_btn_y + back_btn_height:
            self.navigate_to_main()
            return

        # Calculate button positions - MUST MATCH draw_level_options() coordinates!
        button_width = 200
        button_height = 50
        button_spacing = 30
        center_x = self.SCREEN_WIDTH // 2

        # Level info card coordinates (from draw_level_options)
        card_height = 200
        card_y = 180
        buttons_y = card_y + card_height + 50

        # "Play Original" button - MATCHES drawing coordinates
        play_btn_x = center_x - button_width // 2
        play_btn_y = buttons_y
        if (play_btn_x <= x <= play_btn_x + button_width and
                play_btn_y <= y <= play_btn_y + button_height):
            # Launch the original level
            self.show_level_preview = True
            self.preview_config = self.level_options_config
            self.preview_scroll_offset = 0
            return

        # "View Variants" button (only show if variants exist)
        base_level = self.get_base_level_for_config(self.level_options_config)
        has_variants = base_level in self.variants and len(
            self.variants[base_level]) > 0

        current_button_y = buttons_y + button_height + button_spacing

        if has_variants:
            variants_btn_x = center_x - button_width // 2
            variants_btn_y = current_button_y
            if (variants_btn_x <= x <= variants_btn_x + button_width and
                    variants_btn_y <= y <= variants_btn_y + button_height):
                # Navigate to variants view
                self.navigate_to_variants(base_level)
                return

            current_button_y += button_height + button_spacing

        # "Create Variant" button
        create_variant_btn_x = center_x - button_width // 2
        create_variant_btn_y = current_button_y
        if (create_variant_btn_x <= x <= create_variant_btn_x + button_width and
                create_variant_btn_y <= y <= create_variant_btn_y + button_height):
            # Open variant creation UI
            self.variant_ui.open_for_level(self.level_options_config['path'])
            self.show_variant_selector = True
            return

    def handle_preview_click(self, pos):
        """Handle mouse clicks in the level preview screen"""
        x, y = pos

        # Check for back button click (top-left)
        if 50 <= x <= 150 and 50 <= y <= 80:
            self.show_level_preview = False
            self.preview_config = None
            return

        # Check for play button click (center-bottom) - match the drawing coordinates exactly
        play_btn_width = 200
        play_btn_height = 50
        play_btn_x = self.SCREEN_WIDTH // 2 - play_btn_width // 2
        play_btn_y = self.SCREEN_HEIGHT - 120
        if play_btn_x <= x <= play_btn_x + play_btn_width and play_btn_y <= y <= play_btn_y + play_btn_height:
            if self.preview_config:
                self.launch_game(self.preview_config)
            return

    def handle_variant_creation(self):
        """Handle creation of a new variant"""
        try:
            variant = self.variant_ui.get_selected_variant()
            if variant:
                # The variant is already created and saved by the UI
                print(f"Variant created: {variant.variant_name}")
                print(
                    f"Difficulty: {variant.total_difficulty_multiplier:.1f}x")
                print(f"Reward: {variant.total_reward_multiplier:.1f}x")
                print(f"Path: {variant.config_path}")

                # Close variant selector
                self.show_variant_selector = False
                self.variant_ui.close()

                # Reload configurations to include the new variant
                self.load_configurations()

                # Show status message
                self.generation_message = f"✅ Variant created: {variant.variant_name}"
                self.show_generation_status = True

        except Exception as e:
            print(f"Error creating variant: {e}")
            self.generation_message = f"❌ Error creating variant: {e}"
            self.show_generation_status = True

    def navigate_to_variants(self, base_level_name: str):
        """Navigate to variants view for a specific base level"""
        self.current_view = "variants"
        self.selected_base_level = base_level_name
        self.selected_config = None
        self.scroll_offset = 0
        self.update_scroll_limits()

    def navigate_to_main(self):
        """Navigate back to main view (base levels)"""
        self.current_view = "main"
        self.selected_base_level = None
        self.level_options_config = None
        self.selected_config = None
        self.scroll_offset = 0
        self.update_scroll_limits()

    def navigate_to_level_options(self, config):
        """Navigate to level options view (play original vs view variants)"""
        self.current_view = "level_options"
        self.level_options_config = config
        self.selected_config = None
        self.scroll_offset = 0

    def get_current_config_list(self):
        """Get the current list of configs to display based on view"""
        if self.current_view == "main":
            return self.configs
        else:  # variants view
            base_level = self.selected_base_level
            return self.variants.get(base_level, [])

    def get_base_level_for_config(self, config):
        """Extract base level name from a config"""
        if config.get('is_variant'):
            metadata = config.get('config_data', {}).get(
                '_variant_metadata', {})
            return metadata.get('base_level', 'unknown')
        else:
            # For base configs, derive base level name from filename
            filename = config['filename']
            if filename == 'tower_defense_game.json':
                return 'tower_defense_game'
            elif filename == 'test_config.json':
                return 'test_config'
            elif filename.replace('.json', '').isdigit():
                return f"level_{filename.replace('.json', '')}"
            else:
                return filename.replace('.json', '').split('_')[0]

    def update_scroll(self):
        """Update scroll offset to keep selected item visible"""
        if self.selected_config is None:
            return

        visible_items = 8

        # If selected item is above visible area
        if self.selected_config < self.scroll_offset:
            self.scroll_offset = self.selected_config

        # If selected item is below visible area
        elif self.selected_config >= self.scroll_offset + visible_items:
            self.scroll_offset = self.selected_config - visible_items + 1

        # Clamp scroll offset
        self.scroll_offset = max(0, min(self.max_scroll, self.scroll_offset))

    def launch_game(self, config_info: Dict):
        """Launch the game with the selected configuration"""
        print(f"Launching game with config: {config_info['name']}")

        # Set the config file for the game to use
        from config.game_config import set_config_file
        set_config_file(config_info['path'])

        # Import and run the game
        from game import Game

        # Close launcher window
        pygame.display.quit()

        # Run the game (skip config selection since launcher already set it)
        game = Game(skip_config_selection=True, launched_from_menu=True)
        # Pass the global upgrade system to the game
        game.global_upgrade_system = self.global_upgrade_system
        game.run()

        # Game finished - check if we should generate adaptive config
        self.handle_post_game()

        # Reinitialize pygame for launcher
        pygame.display.init()
        self.screen = pygame.display.set_mode(
            (self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("Tower Defense - Game Launcher")

        # Reload configurations and performance stats (in case new ones were generated)
        self.load_configurations()
        self.load_performance_stats()

    def handle_post_game(self):
        """Handle actions after game completion"""
        # Check for new performance data using completion marker
        performance_dir = os.path.join("ai", "performance_data")
        marker_path = os.path.join(performance_dir, "last_completion.txt")

        if os.path.exists(marker_path):
            try:
                # Check if this is a new completion since we last checked
                with open(marker_path, 'r') as f:
                    lines = f.read().strip().split('\n')
                    if len(lines) >= 2:
                        completion_timestamp = lines[0]
                        performance_filename = lines[1]

                        # Check if we've already processed this completion
                        processed_marker_path = os.path.join(
                            performance_dir, "last_processed.txt")
                        should_process = True

                        if os.path.exists(processed_marker_path):
                            with open(processed_marker_path, 'r') as pf:
                                last_processed = pf.read().strip()
                                if last_processed == completion_timestamp:
                                    should_process = False  # Already processed this completion

                        if should_process:
                            print(
                                f"Detected new game completion at {completion_timestamp}")
                            print(
                                f"Performance saved as: {performance_filename}")

                            # Mark this completion as processed
                            with open(processed_marker_path, 'w') as pf:
                                pf.write(completion_timestamp)

                            # Automatically generate adaptive config using multi-game analysis
                            self.auto_generate_multi_game_config()
                        else:
                            print("No new game completions detected")

            except Exception as e:
                print(f"Error checking completion marker: {e}")
        else:
            print("No completion marker found - no recent game completions")

    def auto_generate_multi_game_config(self):
        """Generate adaptive config based on up to 5 recent games (recommended)"""
        try:
            print("DEBUG: Starting generate_multi_game_adaptive_config (button method)")
            from ai.adaptive_config_generator import AdaptiveConfigGenerator

            print(
                f"DEBUG: recent_performance_stats: {self.recent_performance_stats}")
            if not self.recent_performance_stats:
                print("DEBUG: No performance data found")
                self.generation_message = "❌ No performance data found. Play some games first!"
                self.show_generation_status = True
                return

            print(
                f"DEBUG: Generating multi-game adaptive config from {self.recent_performance_stats['games_count']} recent games...")

            # Generate adaptive config using multi-game analysis with Modular AI ONLY (testing)
            print("DEBUG: Creating AdaptiveConfigGenerator...")
            generator = AdaptiveConfigGenerator(
                use_modular_ai=True, use_full_ai=False)
            print("DEBUG: Calling generator.generate_config_from_recent_games()...")
            config = generator.generate_config_from_recent_games()
            print(f"DEBUG: Generator returned: {config is not None}")

            if config:
                avg_score = self.recent_performance_stats['average_score']
                trend = self.recent_performance_stats['trend']
                method = config.get('_adaptive_metadata', {}).get(
                    'generation_method', 'unknown')
                method_emoji = "🧩" if 'modular_ai' in method else "🤖" if 'ai' in method else "🔄"
                self.generation_message = f"✅ {method_emoji} AI level generated! Avg: {avg_score:.1f}%, Trend: {trend}"
                self.show_generation_status = True
                print(f"DEBUG: Generated config with method: {method}")

                # Reload configurations and performance stats
                print("DEBUG: Reloading configurations...")
                self.load_configurations()
                print("DEBUG: Reloading performance stats...")
                self.load_performance_stats()
                print(
                    f"Multi-game adaptive configuration generated (Avg: {avg_score:.1f}%, Trend: {trend})")
            else:
                print("DEBUG: Generator returned None - generation failed")
                self.generation_message = "❌ Failed to generate adaptive config"
                self.show_generation_status = True

        except Exception as e:
            print(
                f"DEBUG: Exception in generate_multi_game_adaptive_config: {e}")
            import traceback
            traceback.print_exc()
            print(f"Error generating adaptive config: {e}")
            self.generation_message = f"❌ Error generating adaptive config: {e}"
            self.show_generation_status = True

    def generate_multi_game_adaptive_config(self):
        """Generate adaptive config based on up to 5 recent games (recommended)"""
        try:
            print("DEBUG: Starting generate_multi_game_adaptive_config (button method)")
            from ai.adaptive_config_generator import AdaptiveConfigGenerator

            print(
                f"DEBUG: recent_performance_stats: {self.recent_performance_stats}")
            if not self.recent_performance_stats:
                print("DEBUG: No performance data found")
                self.generation_message = "❌ No performance data found. Play some games first!"
                self.show_generation_status = True
                return

            print(
                f"DEBUG: Generating multi-game adaptive config from {self.recent_performance_stats['games_count']} recent games...")

            # Generate adaptive config using multi-game analysis with Modular AI ONLY (testing)
            print("DEBUG: Creating AdaptiveConfigGenerator...")
            generator = AdaptiveConfigGenerator(
                use_modular_ai=True, use_full_ai=False)
            print("DEBUG: Calling generator.generate_config_from_recent_games()...")
            config = generator.generate_config_from_recent_games()
            print(f"DEBUG: Generator returned: {config is not None}")

            if config:
                avg_score = self.recent_performance_stats['average_score']
                trend = self.recent_performance_stats['trend']
                method = config.get('_adaptive_metadata', {}).get(
                    'generation_method', 'unknown')
                method_emoji = "🧩" if 'modular_ai' in method else "🤖" if 'ai' in method else "🔄"
                self.generation_message = f"✅ {method_emoji} AI level generated! Avg: {avg_score:.1f}%, Trend: {trend}"
                self.show_generation_status = True
                print(f"DEBUG: Generated config with method: {method}")

                # Reload configurations and performance stats
                print("DEBUG: Reloading configurations...")
                self.load_configurations()
                print("DEBUG: Reloading performance stats...")
                self.load_performance_stats()
                print(
                    f"Multi-game adaptive configuration generated (Avg: {avg_score:.1f}%, Trend: {trend})")
            else:
                print("DEBUG: Generator returned None - generation failed")
                self.generation_message = "❌ Failed to generate adaptive config"
                self.show_generation_status = True

        except Exception as e:
            print(f"Error generating adaptive config: {e}")
            self.generation_message = f"❌ Error generating adaptive config: {e}"
            self.show_generation_status = True

    def draw(self):
        """Draw the elegant launcher interface"""
        # Soft background
        self.screen.fill(self.BACKGROUND)

        # Show level preview if active
        if self.show_level_preview and self.preview_config:
            self.draw_level_preview()
            return

        # Header section
        header_height = 100
        pygame.draw.rect(self.screen, self.CARD_BG,
                         (0, 0, self.SCREEN_WIDTH, header_height))
        pygame.draw.line(self.screen, self.BORDER_LIGHT, (0,
                         header_height), (self.SCREEN_WIDTH, header_height), 1)

        # Title and subtitle based on current view
        if self.current_view == "main":
            title_text = self.title_font.render(
                "Tower Defense", True, self.TEXT_PRIMARY)
            current_configs = self.get_current_config_list()
            if current_configs:
                subtitle_text = self.info_font.render(
                    "Select a level to configure", True, self.TEXT_SECONDARY)
            else:
                subtitle_text = self.info_font.render(
                    "No levels found", True, self.ACCENT_ORANGE)
        elif self.current_view == "level_options":
            # Show level options view
            level_name = self.level_options_config.get(
                'name', 'Unknown Level') if self.level_options_config else 'Unknown'
            title_text = self.title_font.render(
                level_name, True, self.TEXT_PRIMARY)
            subtitle_text = self.info_font.render(
                "Choose how to play this level", True, self.TEXT_SECONDARY)
        else:  # variants view
            # Show base level name with indicator this is variants view
            base_level_display = (
                self.selected_base_level or "Unknown").replace('_', ' ').title()
            title_text = self.title_font.render(
                f"{base_level_display} - Variants", True, self.TEXT_PRIMARY)
            variant_count = len(self.variants.get(
                self.selected_base_level or "", []))
            subtitle_text = self.info_font.render(
                f"{variant_count} variants available", True, self.TEXT_SECONDARY)

        title_rect = title_text.get_rect(center=(self.SCREEN_WIDTH // 2, 35))
        self.screen.blit(title_text, title_rect)
        subtitle_rect = subtitle_text.get_rect(
            center=(self.SCREEN_WIDTH // 2, 65))
        self.screen.blit(subtitle_text, subtitle_rect)

        # Draw back button in variants view or level options view
        if self.current_view == "variants" or self.current_view == "level_options":
            self.draw_back_button()

        # Action buttons (draw in ALL views)
        self.draw_action_buttons()

        # Show level options view if active
        if self.current_view == "level_options":
            self.draw_level_options()
        else:
            # Main content area (only for main and variants views)
            content_y = header_height + 20
            content_height = self.SCREEN_HEIGHT - \
                header_height - 120  # Leave space for buttons

            # In variants view, leave space for back button
            if self.current_view == "variants":
                content_y += 50
                content_height -= 50

            current_configs = self.get_current_config_list()
            if current_configs:
                self.draw_level_cards(content_y, content_height)

        # Performance panel (if visible)
        if self.show_performance_panel:
            if self.recent_performance_stats:
                self.draw_performance_panel()
            else:
                # Show a message when no performance data is available
                self.draw_no_performance_data_message()

        # Upgrade menu (if visible)
        if self.show_upgrade_menu:
            try:
                self.global_upgrade_ui.draw(self.screen)
            except Exception as e:
                print(f"Error drawing upgrade menu: {e}")

        # If upgrade menu was closed from within the UI, reset the flag
        if self.show_upgrade_menu and not self.global_upgrade_ui.is_open:
            self.show_upgrade_menu = False

        # Variant selector (if visible)
        if self.show_variant_selector:
            self.variant_ui.draw(self.screen)

        # Status message
        if self.show_generation_status and self.generation_message:
            self.draw_status_message()

        pygame.display.flip()

    def draw_back_button(self):
        """Draw back button in variants view"""
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 35

        # Button background
        pygame.draw.rect(self.screen, self.BORDER_DARK, (back_btn_x,
                         back_btn_y, back_btn_width, back_btn_height), border_radius=8)
        pygame.draw.rect(self.screen, self.CARD_BG, (back_btn_x + 1, back_btn_y +
                         1, back_btn_width - 2, back_btn_height - 2), border_radius=7)

        # Button text
        back_text = self.info_font.render("← Back", True, self.TEXT_PRIMARY)
        back_rect = back_text.get_rect(
            center=(back_btn_x + back_btn_width // 2, back_btn_y + back_btn_height // 2))
        self.screen.blit(back_text, back_rect)

    def draw_level_options(self):
        """Draw the level options view (play original vs view variants)"""
        if not self.level_options_config:
            return

        # Level info card
        card_width = 600
        card_height = 200
        card_x = (self.SCREEN_WIDTH - card_width) // 2
        card_y = 180

        # Card background
        pygame.draw.rect(self.screen, self.CARD_BG, (card_x,
                         card_y, card_width, card_height), border_radius=12)
        pygame.draw.rect(self.screen, self.BORDER_LIGHT, (card_x,
                         card_y, card_width, card_height), 2, border_radius=12)

        # Level details
        config = self.level_options_config
        margin = 30

        # Level name (already in title, but show description)
        desc_y = card_y + margin
        description = config.get(
            'description', 'A challenging tower defense level.')
        desc_lines = self.wrap_text(
            description, self.menu_font, card_width - 2 * margin)
        for line in desc_lines:
            desc_text = self.menu_font.render(line, True, self.TEXT_SECONDARY)
            self.screen.blit(desc_text, (card_x + margin, desc_y))
            desc_y += 25

        # Difficulty bar
        difficulty = config.get('difficulty', 50)
        diff_text = self.info_font.render(
            f"Difficulty: {difficulty}/100", True, self.TEXT_SECONDARY)
        self.screen.blit(diff_text, (card_x + margin, desc_y + 10))
        self.draw_modern_difficulty_bar(
            card_x + margin, desc_y + 35, difficulty, card_width - 2 * margin, 8)

        # Action buttons
        button_width = 200
        button_height = 50
        button_spacing = 30
        center_x = self.SCREEN_WIDTH // 2
        buttons_y = card_y + card_height + 50

        # "Play Original" button
        play_btn_x = center_x - button_width // 2
        play_btn_y = buttons_y
        pygame.draw.rect(self.screen, self.ACCENT_GREEN, (play_btn_x,
                         play_btn_y, button_width, button_height), border_radius=25)

        play_text = self.subtitle_font.render(
            "Play Original", True, self.CARD_BG)
        play_rect = play_text.get_rect(
            center=(play_btn_x + button_width // 2, play_btn_y + button_height // 2))
        self.screen.blit(play_text, play_rect)

        # "View Variants" button (only if variants exist)
        base_level = self.get_base_level_for_config(config)
        has_variants = base_level in self.variants and len(
            self.variants[base_level]) > 0

        current_button_y = buttons_y + button_height + button_spacing

        if has_variants:
            variants_btn_x = center_x - button_width // 2
            variants_btn_y = current_button_y
            variant_count = len(self.variants[base_level])

            pygame.draw.rect(self.screen, self.ACCENT_BLUE, (variants_btn_x,
                             variants_btn_y, button_width, button_height), border_radius=25)

            variants_text = self.subtitle_font.render(
                f"View Variants ({variant_count})", True, self.CARD_BG)
            variants_rect = variants_text.get_rect(center=(
                variants_btn_x + button_width // 2, variants_btn_y + button_height // 2))
            self.screen.blit(variants_text, variants_rect)

            current_button_y += button_height + button_spacing

        # "Create Variant" button
        create_variant_btn_x = center_x - button_width // 2
        create_variant_btn_y = current_button_y

        pygame.draw.rect(self.screen, (138, 43, 226), (create_variant_btn_x,
                         create_variant_btn_y, button_width, button_height), border_radius=25)

        create_variant_text = self.subtitle_font.render(
            "Create Variant", True, self.CARD_BG)
        create_variant_rect = create_variant_text.get_rect(center=(
            create_variant_btn_x + button_width // 2, create_variant_btn_y + button_height // 2))
        self.screen.blit(create_variant_text, create_variant_rect)

        # Instructions
        instructions_y = current_button_y + button_height + 20

        instructions = "Click a button above to proceed, or use the back button to return to the main menu"
        instr_text = self.small_font.render(
            instructions, True, self.TEXT_SECONDARY)
        instr_rect = instr_text.get_rect(
            center=(self.SCREEN_WIDTH // 2, instructions_y))
        self.screen.blit(instr_text, instr_rect)

    def draw_level_preview(self):
        """Draw the level preview screen with description and rewards"""
        if not self.preview_config:
            return

        # Background
        self.screen.fill(self.BACKGROUND)

        # Get level metadata
        metadata = self.preview_config.get('level_metadata', {})

        # Check if this is a variant
        is_variant = '_variant_metadata' in self.preview_config
        variant_metadata = self.preview_config.get(
            '_variant_metadata', {}) if is_variant else {}

        # Header section
        header_height = 100
        pygame.draw.rect(self.screen, self.CARD_BG,
                         (0, 0, self.SCREEN_WIDTH, header_height))
        pygame.draw.line(self.screen, self.BORDER_LIGHT, (0,
                         header_height), (self.SCREEN_WIDTH, header_height), 1)

        # Back button
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 30
        pygame.draw.rect(self.screen, self.BORDER_DARK, (back_btn_x,
                         back_btn_y, back_btn_width, back_btn_height), border_radius=8)
        pygame.draw.rect(self.screen, self.CARD_BG, (back_btn_x + 1, back_btn_y +
                         1, back_btn_width - 2, back_btn_height - 2), border_radius=7)
        back_text = self.info_font.render("← Back", True, self.TEXT_PRIMARY)
        back_rect = back_text.get_rect(
            center=(back_btn_x + back_btn_width // 2, back_btn_y + back_btn_height // 2))
        self.screen.blit(back_text, back_rect)

        # Level title
        if is_variant:
            level_name = variant_metadata.get(
                'variant_name', self.preview_config.get('name', 'Unknown Variant'))
        else:
            level_name = metadata.get(
                'name', self.preview_config.get('name', 'Unknown Level'))
        title_text = self.title_font.render(
            level_name, True, self.TEXT_PRIMARY)
        title_rect = title_text.get_rect(center=(self.SCREEN_WIDTH // 2, 35))
        self.screen.blit(title_text, title_rect)

        # Difficulty rating and variant indicator
        if is_variant:
            base_level = variant_metadata.get('base_level', 'Unknown')
            difficulty_mult = variant_metadata.get(
                'difficulty_multiplier', 1.0)
            reward_mult = variant_metadata.get('reward_multiplier', 1.0)
            difficulty_text = self.info_font.render(
                f"Variant of {base_level} • {difficulty_mult:.1f}x difficulty • {reward_mult:.1f}x reward", True, (138, 43, 226))
        else:
            difficulty_rating = metadata.get('difficulty_rating', 'Unknown')
            difficulty_color = self.ACCENT_GREEN if difficulty_rating == 'Easy' else self.ACCENT_ORANGE if difficulty_rating == 'Normal' else (
                220, 53, 69)
            difficulty_text = self.info_font.render(
                f"Difficulty: {difficulty_rating}", True, difficulty_color)

        difficulty_rect = difficulty_text.get_rect(
            center=(self.SCREEN_WIDTH // 2, 65))
        self.screen.blit(difficulty_text, difficulty_rect)

        # Main content area
        content_margin = 80
        content_width = self.SCREEN_WIDTH - 2 * content_margin
        content_x = content_margin
        content_y = header_height + 40

        # Description section
        if is_variant:
            desc_title = self.subtitle_font.render(
                "Variant Modifiers", True, self.TEXT_PRIMARY)
            self.screen.blit(desc_title, (content_x, content_y))
            content_y += 35

            # Show modifiers applied
            modifiers = variant_metadata.get('modifiers', [])
            if modifiers:
                for modifier in modifiers:
                    mod_name = modifier.get('name', 'Unknown Modifier')
                    mod_desc = modifier.get('description', 'No description')

                    # Modifier name
                    mod_text = self.menu_font.render(
                        f"• {mod_name}", True, self.TEXT_PRIMARY)
                    self.screen.blit(mod_text, (content_x + 20, content_y))
                    content_y += 25

                    # Modifier description
                    desc_lines = self.wrap_text(
                        mod_desc, self.info_font, content_width - 60)
                    for line in desc_lines:
                        desc_text = self.info_font.render(
                            line, True, self.TEXT_SECONDARY)
                        self.screen.blit(
                            desc_text, (content_x + 40, content_y))
                        content_y += 20
                    content_y += 10
            else:
                no_mods_text = self.menu_font.render(
                    "No modifiers applied", True, self.TEXT_SECONDARY)
                self.screen.blit(no_mods_text, (content_x + 20, content_y))
                content_y += 30
        else:
            desc_title = self.subtitle_font.render(
                "Description", True, self.TEXT_PRIMARY)
            self.screen.blit(desc_title, (content_x, content_y))
            content_y += 35

            # Description text (wrap to multiple lines)
            description = metadata.get(
                'description', 'No description available.')
            desc_lines = self.wrap_text(
                description, self.menu_font, content_width - 40)
            for line in desc_lines:
                desc_text = self.menu_font.render(
                    line, True, self.TEXT_SECONDARY)
                self.screen.blit(desc_text, (content_x + 20, content_y))
                content_y += 25

        content_y += 20

        # Level details section
        details_title = self.subtitle_font.render(
            "Level Details", True, self.TEXT_PRIMARY)
        self.screen.blit(details_title, (content_x, content_y))
        content_y += 35

        # Create two columns for details
        col1_x = content_x + 20
        col2_x = content_x + content_width // 2 + 20

        if is_variant:
            # Column 1: Base level info
            base_level_text = self.info_font.render(
                f"Base Level: {variant_metadata.get('base_level', 'Unknown')}", True, self.TEXT_SECONDARY)
            self.screen.blit(base_level_text, (col1_x, content_y))

            generation_time = variant_metadata.get('generation_timestamp', '')
            if generation_time:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(
                        generation_time.replace('Z', '+00:00'))
                    time_str = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    time_str = 'Unknown'
            else:
                time_str = 'Unknown'
            time_text = self.info_font.render(
                f"Created: {time_str}", True, self.TEXT_SECONDARY)
            self.screen.blit(time_text, (col1_x, content_y + 25))

            # Column 2: Difficulty and reward multipliers
            base_diff = variant_metadata.get('base_difficulty', 50)
            final_diff = variant_metadata.get('final_difficulty', 50)
            diff_text = self.info_font.render(
                f"Difficulty: {base_diff} → {final_diff}", True, self.TEXT_SECONDARY)
            self.screen.blit(diff_text, (col2_x, content_y))

            estimated_time = variant_metadata.get(
                'estimated_playtime', 'Unknown')
            duration_text = self.info_font.render(
                f"Duration: {estimated_time}", True, self.TEXT_SECONDARY)
            self.screen.blit(duration_text, (col2_x, content_y + 25))
        else:
            # Column 1: Duration, Recommended for (original behavior)
            duration_text = self.info_font.render(
                f"Duration: {metadata.get('estimated_duration', 'Unknown')}", True, self.TEXT_SECONDARY)
            self.screen.blit(duration_text, (col1_x, content_y))

            recommended_text = self.info_font.render(
                f"Recommended for: {metadata.get('recommended_for', 'all players')}", True, self.TEXT_SECONDARY)
            self.screen.blit(recommended_text, (col1_x, content_y + 25))

            # Column 2: Difficulty score, Victory points
            difficulty_score = self.preview_config.get('difficulty', 50)
            score_text = self.info_font.render(
                f"Difficulty Score: {difficulty_score}/100", True, self.TEXT_SECONDARY)
            self.screen.blit(score_text, (col2_x, content_y))

            victory_points = metadata.get(
                'victory_rewards', {}).get('victory_points', 1)
            points_text = self.info_font.render(
                f"Victory Points: {victory_points}", True, self.ACCENT_GREEN)
            self.screen.blit(points_text, (col2_x, content_y + 25))

        content_y += 70

        # Rewards section
        rewards_title = self.subtitle_font.render(
            "Victory Rewards", True, self.TEXT_PRIMARY)
        self.screen.blit(rewards_title, (content_x, content_y))
        content_y += 35

        if is_variant:
            # Calculate variant rewards
            base_victory_points = metadata.get(
                'victory_rewards', {}).get('victory_points', 1)
            reward_mult = variant_metadata.get('reward_multiplier', 1.0)
            variant_points = int(base_victory_points * reward_mult)

            points_desc = f"Earn {variant_points} Victory Points ({base_victory_points} × {reward_mult:.1f}x modifier)"
            points_desc_text = self.menu_font.render(
                points_desc, True, self.ACCENT_GREEN)
            self.screen.blit(points_desc_text, (content_x + 20, content_y))
            content_y += 30

            # Variant completion bonuses
            bonuses = [
                "Bonus Victory Points for difficulty",
                "Achievement progress",
                "Variant mastery experience"
            ]
            for bonus in bonuses[:3]:
                bonus_text = self.info_font.render(
                    f"• {bonus}", True, self.TEXT_SECONDARY)
                self.screen.blit(bonus_text, (content_x + 20, content_y))
                content_y += 22
        else:
            # Victory points description (original behavior)
            victory_rewards = metadata.get('victory_rewards', {})
            victory_points = victory_rewards.get('victory_points', 1)
            points_desc = victory_rewards.get(
                'victory_points_description', f'Earn {victory_points} Victory Points')
            points_desc_text = self.menu_font.render(
                points_desc, True, self.ACCENT_GREEN)
            self.screen.blit(points_desc_text, (content_x + 20, content_y))
            content_y += 30

            # Completion bonuses
            bonuses = victory_rewards.get('completion_bonuses', [])
            if bonuses:
                for bonus in bonuses[:3]:  # Show up to 3 bonuses
                    bonus_text = self.info_font.render(
                        f"• {bonus}", True, self.TEXT_SECONDARY)
                    self.screen.blit(bonus_text, (content_x + 20, content_y))
                    content_y += 22

        content_y += 20

        # Special features or tips section
        if is_variant:
            # Show variant-specific information
            features_title = self.subtitle_font.render(
                "Variant Info", True, self.TEXT_PRIMARY)
            self.screen.blit(features_title, (content_x, content_y))
            content_y += 35

            info_items = [
                f"Modifiers applied: {len(modifiers)}",
                f"Difficulty multiplier: {variant_metadata.get('difficulty_multiplier', 1.0):.1f}x",
                f"Reward multiplier: {variant_metadata.get('reward_multiplier', 1.0):.1f}x"
            ]

            for item in info_items:
                item_text = self.info_font.render(
                    f"• {item}", True, self.TEXT_SECONDARY)
                self.screen.blit(item_text, (content_x + 20, content_y))
                content_y += 22
        else:
            # Special features section (original behavior)
            features = metadata.get('special_features', [])
            if features:
                features_title = self.subtitle_font.render(
                    "Special Features", True, self.TEXT_PRIMARY)
                self.screen.blit(features_title, (content_x, content_y))
                content_y += 35

                for feature in features[:4]:  # Show up to 4 features
                    feature_text = self.info_font.render(
                        f"• {feature}", True, self.TEXT_SECONDARY)
                    self.screen.blit(feature_text, (content_x + 20, content_y))
                    content_y += 22

        # Play button (large, centered at bottom)
        play_btn_width = 200
        play_btn_height = 50
        play_btn_x = self.SCREEN_WIDTH // 2 - play_btn_width // 2
        play_btn_y = self.SCREEN_HEIGHT - 120

        pygame.draw.rect(self.screen, self.ACCENT_GREEN, (play_btn_x,
                         play_btn_y, play_btn_width, play_btn_height), border_radius=25)

        # Play button text
        play_text = self.subtitle_font.render("START GAME", True, self.CARD_BG)
        play_rect = play_text.get_rect(
            center=(play_btn_x + play_btn_width // 2, play_btn_y + play_btn_height // 2))
        self.screen.blit(play_text, play_rect)

        # Tips section (if space allows and not a variant)
        if not is_variant:
            tips = metadata.get('tips', [])
            if tips and content_y < self.SCREEN_HEIGHT - 200:
                tips_title = self.subtitle_font.render(
                    "Tips", True, self.TEXT_PRIMARY)
                self.screen.blit(tips_title, (content_x, content_y))
                content_y += 35

                for tip in tips[:2]:  # Show up to 2 tips
                    if content_y > self.SCREEN_HEIGHT - 180:
                        break
                    tip_text = self.info_font.render(
                        f"• {tip}", True, self.TEXT_SECONDARY)
                    self.screen.blit(tip_text, (content_x + 20, content_y))
                    content_y += 22

        pygame.display.flip()

    def wrap_text(self, text: str, font, max_width: int) -> list:
        """Wrap text to fit within a given width"""
        words = text.split(' ')
        lines = []
        current_line = []

        for word in words:
            test_line = ' '.join(current_line + [word])
            if font.size(test_line)[0] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    lines.append(word)

        if current_line:
            lines.append(' '.join(current_line))

        return lines

    def draw_level_cards(self, start_y: int, available_height: int):
        """Draw level selection cards in an elegant grid"""
        card_width = 250
        card_height = 120
        padding = 20
        margin = 40

        # Get current configs to display
        current_configs = self.get_current_config_list()

        # Calculate grid layout
        cards_per_row = (self.SCREEN_WIDTH - 2 *
                         margin) // (card_width + padding)
        rows_visible = available_height // (card_height + padding)

        # Draw visible cards
        for i in range(min(len(current_configs), cards_per_row * rows_visible)):
            config_index = i + self.scroll_offset
            if config_index >= len(current_configs):
                break

            config = current_configs[config_index]

            # Card position
            row = i // cards_per_row
            col = i % cards_per_row

            x = margin + col * (card_width + padding)
            y = start_y + row * (card_height + padding)

            # Check if this card is being pressed
            is_pressed = self.pressed_card == config_index

            # Apply press effect (smaller size and offset)
            press_offset = 2 if is_pressed else 0
            actual_x = x + press_offset
            actual_y = y + press_offset
            actual_width = card_width - (press_offset * 2)
            actual_height = card_height - (press_offset * 2)

            # Card background
            card_color = self.CARD_BG
            if is_pressed:
                # Darker color when pressed
                card_color = (int(
                    self.CARD_BG[0] * 0.9), int(self.CARD_BG[1] * 0.9), int(self.CARD_BG[2] * 0.9))

            if config_index == self.selected_config:
                # Draw selection highlight
                pygame.draw.rect(self.screen, self.SELECTED_BG,
                                 (x-2, y-2, card_width+4, card_height+4))
                pygame.draw.rect(self.screen, self.ACCENT_BLUE,
                                 (x-2, y-2, card_width+4, card_height+4), 2)

            pygame.draw.rect(self.screen, card_color,
                             (actual_x, actual_y, actual_width, actual_height))
            pygame.draw.rect(self.screen, self.BORDER_LIGHT,
                             (actual_x, actual_y, actual_width, actual_height), 1)

            # Level name
            name_text = self.menu_font.render(
                config['name'][:25] + ("..." if len(config['name']) > 25 else ""), True, self.TEXT_PRIMARY)
            self.screen.blit(name_text, (actual_x + 15, actual_y + 15))

            # Difficulty indicator
            difficulty_text = self.small_font.render(
                f"Difficulty: {config['difficulty']}", True, self.TEXT_SECONDARY)
            self.screen.blit(difficulty_text, (actual_x + 15, actual_y + 40))

            # Difficulty bar (modern style)
            bar_x, bar_y = actual_x + 15, actual_y + 55
            bar_width, bar_height = actual_width - 30, 6
            self.draw_modern_difficulty_bar(
                bar_x, bar_y, config['difficulty'], bar_width, bar_height)

            # AI/Variant indicators
            badge_x = actual_x + actual_width - 50
            badge_y = actual_y + 10

            if config.get('is_variant', False):
                # Variant badge (purple)
                pygame.draw.rect(self.screen, (138, 43, 226),
                                 (badge_x, badge_y, 40, 18), border_radius=9)
                variant_text = self.small_font.render(
                    "VAR", True, self.CARD_BG)
                variant_rect = variant_text.get_rect(
                    center=(badge_x + 20, badge_y + 9))
                self.screen.blit(variant_text, variant_rect)

                # Completed indicator if applicable
                if config.get('variant_completed', False):
                    completed_x = badge_x - 45
                    pygame.draw.rect(self.screen, self.ACCENT_GREEN,
                                     (completed_x, badge_y, 35, 18), border_radius=9)
                    check_text = self.small_font.render(
                        "✓", True, self.CARD_BG)
                    check_rect = check_text.get_rect(
                        center=(completed_x + 17, badge_y + 9))
                    self.screen.blit(check_text, check_rect)

            elif config['is_adaptive']:
                # AI badge (blue)
                pygame.draw.rect(self.screen, self.ACCENT_BLUE,
                                 (badge_x, badge_y, 35, 18), border_radius=9)
                ai_text = self.small_font.render("AI", True, self.CARD_BG)
                ai_rect = ai_text.get_rect(center=(badge_x + 17, badge_y + 9))
                self.screen.blit(ai_text, ai_rect)

            # Description (truncated)
            desc_text = self.small_font.render(config['description'][:35] + (
                "..." if len(config['description']) > 35 else ""), True, self.TEXT_SECONDARY)
            self.screen.blit(desc_text, (actual_x + 15, actual_y + 80))

            # Show variant count indicator for main menu base levels
            if self.current_view == "main" and not config.get('is_variant'):
                # Check if this base level has variants
                base_level = self.get_base_level_for_config(config)
                has_variants = base_level in self.variants and len(
                    self.variants[base_level]) > 0

                if has_variants:
                    # Show variant count in bottom right
                    variant_count = len(self.variants[base_level])
                    count_text = self.small_font.render(
                        f"{variant_count} variants", True, self.TEXT_SECONDARY)
                    count_rect = count_text.get_rect(
                        right=actual_x + actual_width - 15, bottom=actual_y + actual_height - 10)
                    self.screen.blit(count_text, count_rect)
            elif self.current_view == "variants":
                # In variants view, show play button
                play_btn_x = actual_x + actual_width - 60
                play_btn_y = actual_y + actual_height - 35
                pygame.draw.rect(self.screen, self.ACCENT_GREEN,
                                 (play_btn_x, play_btn_y, 45, 25), border_radius=12)
                play_text = self.small_font.render("PLAY", True, self.CARD_BG)
                play_rect = play_text.get_rect(
                    center=(play_btn_x + 22, play_btn_y + 12))
                self.screen.blit(play_text, play_rect)

    def draw_modern_difficulty_bar(self, x: int, y: int, difficulty: int, width: int = 100, height: int = 6):
        """Draw a modern, sleek difficulty bar"""
        if not isinstance(difficulty, (int, float)):
            difficulty = 50
        difficulty = max(0, min(100, int(difficulty)))

        # Background bar
        pygame.draw.rect(self.screen, self.BORDER_LIGHT,
                         (x, y, width, height), border_radius=height//2)

        # Progress fill
        fill_width = int((difficulty / 100) * width)
        if fill_width > 0:
            if difficulty <= 30:
                color = self.ACCENT_GREEN
            elif difficulty <= 70:
                color = self.ACCENT_ORANGE
            else:
                color = (220, 53, 69)  # Red for high difficulty

            pygame.draw.rect(
                self.screen, color, (x, y, fill_width, height), border_radius=height//2)

    def draw_action_buttons(self):
        """Draw modern action buttons at the bottom"""
        import time

        # Clear pressed states after brief feedback duration
        if self.pressed_button and time.time() - self.button_press_time > 0.3:
            self.pressed_button = None
        if self.pressed_card and time.time() - self.card_press_time > 0.3:
            self.pressed_card = None

        button_area_height = 80
        button_y = self.SCREEN_HEIGHT - button_area_height

        # Background for button area
        pygame.draw.rect(self.screen, self.CARD_BG,
                         (0, button_y, self.SCREEN_WIDTH, button_area_height))
        pygame.draw.line(self.screen, self.BORDER_LIGHT,
                         (0, button_y), (self.SCREEN_WIDTH, button_y), 1)

        # Button specifications
        button_height = 35
        button_y_pos = button_y + 22
        button_spacing = 20

        # Calculate button positions (centered) - 3 buttons now
        btn1_width = 200  # Generate New Level
        btn2_width = 120  # Stats
        btn3_width = 120  # Upgrades
        total_width = btn1_width + btn2_width + btn3_width + button_spacing * 2
        start_x = (self.SCREEN_WIDTH - total_width) // 2

        # Generate button (primary)
        btn1_x = start_x
        is_pressed = self.pressed_button == 'generate'
        btn_color = (int(self.ACCENT_BLUE[0] * 0.8), int(self.ACCENT_BLUE[1] * 0.8), int(
            self.ACCENT_BLUE[2] * 0.8)) if is_pressed else self.ACCENT_BLUE

        pygame.draw.rect(self.screen, btn_color, (btn1_x, button_y_pos,
                         btn1_width, button_height), border_radius=8)

        btn1_text = self.info_font.render(
            "Generate New Level", True, self.CARD_BG)
        offset = 2 if is_pressed else 0
        btn1_rect = btn1_text.get_rect(center=(
            btn1_x + btn1_width//2 + offset, button_y_pos + button_height//2 + offset))
        self.screen.blit(btn1_text, btn1_rect)

        # Performance/Stats button
        btn2_x = btn1_x + btn1_width + button_spacing
        is_pressed = self.pressed_button == 'stats'
        perf_color = (int(self.ACCENT_GREEN[0] * 0.8), int(self.ACCENT_GREEN[1] * 0.8), int(
            self.ACCENT_GREEN[2] * 0.8)) if is_pressed else self.ACCENT_GREEN

        pygame.draw.rect(self.screen, perf_color, (btn2_x,
                         button_y_pos, btn2_width, button_height), border_radius=8)

        if not self.show_performance_panel and not is_pressed:
            pygame.draw.rect(self.screen, self.CARD_BG, (btn2_x + 1, button_y_pos +
                             1, btn2_width - 2, button_height - 2), border_radius=7)

        offset = 2 if is_pressed else 0
        btn2_text = self.info_font.render(
            "Stats", True, self.CARD_BG if self.show_performance_panel or is_pressed else self.TEXT_PRIMARY)
        btn2_rect = btn2_text.get_rect(center=(
            btn2_x + btn2_width//2 + offset, button_y_pos + button_height//2 + offset))
        self.screen.blit(btn2_text, btn2_rect)

        # Upgrade button
        btn3_x = btn2_x + btn2_width + button_spacing
        is_pressed = self.pressed_button == 'upgrades'
        upgrade_color = (int(self.ACCENT_ORANGE[0] * 0.8), int(self.ACCENT_ORANGE[1] * 0.8), int(
            self.ACCENT_ORANGE[2] * 0.8)) if is_pressed else self.ACCENT_ORANGE

        pygame.draw.rect(self.screen, upgrade_color, (btn3_x,
                         button_y_pos, btn3_width, button_height), border_radius=8)

        if not self.show_upgrade_menu and not is_pressed:
            pygame.draw.rect(self.screen, self.CARD_BG, (btn3_x + 1, button_y_pos +
                             1, btn3_width - 2, button_height - 2), border_radius=7)

        offset = 2 if is_pressed else 0
        btn3_text = self.info_font.render(
            "Upgrades", True, self.CARD_BG if self.show_upgrade_menu or is_pressed else self.TEXT_PRIMARY)
        btn3_rect = btn3_text.get_rect(center=(
            btn3_x + btn3_width//2 + offset, button_y_pos + button_height//2 + offset))
        self.screen.blit(btn3_text, btn3_rect)

        # Store button coordinates for click detection
        self.button_coords = {
            'generate': (btn1_x, button_y_pos, btn1_width, button_height),
            'stats': (btn2_x, button_y_pos, btn2_width, button_height),
            'upgrades': (btn3_x, button_y_pos, btn3_width, button_height)
        }

    def draw_status_message(self):
        """Draw status message with modern styling"""
        if not self.generation_message:
            return

        # Create a large, centered overlay for better visibility
        overlay_height = 80
        overlay_y = self.SCREEN_HEIGHT // 2 - overlay_height // 2

        # Semi-transparent background
        overlay = pygame.Surface((self.SCREEN_WIDTH, overlay_height))
        overlay.set_alpha(220)
        overlay.fill(
            self.ACCENT_GREEN if "✅" in self.generation_message else self.ACCENT_ORANGE if "❌" in self.generation_message else self.ACCENT_BLUE)
        self.screen.blit(overlay, (0, overlay_y))

        # Message text (larger and centered)
        status_text = self.subtitle_font.render(
            self.generation_message, True, self.CARD_BG)
        status_rect = status_text.get_rect(
            center=(self.SCREEN_WIDTH // 2, overlay_y + overlay_height // 2))
        self.screen.blit(status_text, status_rect)

        # Add instruction text
        instruction_text = self.info_font.render(
            "Click anywhere to dismiss", True, self.CARD_BG)
        instruction_rect = instruction_text.get_rect(
            center=(self.SCREEN_WIDTH // 2, overlay_y + overlay_height // 2 + 25))
        self.screen.blit(instruction_text, instruction_rect)

    def draw_performance_panel(self):
        """Draw the modern performance statistics panel"""
        if not self.recent_performance_stats:
            return

        # Modern panel dimensions and positioning
        panel_width = 380
        panel_height = 320
        panel_x = self.SCREEN_WIDTH - panel_width - 30
        panel_y = 140

        # Panel background with shadow effect
        shadow_offset = 4
        pygame.draw.rect(self.screen, (0, 0, 0, 30), (panel_x + shadow_offset,
                         panel_y + shadow_offset, panel_width, panel_height), border_radius=12)
        pygame.draw.rect(self.screen, self.CARD_BG, (panel_x,
                         panel_y, panel_width, panel_height), border_radius=12)
        pygame.draw.rect(self.screen, self.BORDER_LIGHT, (panel_x,
                         panel_y, panel_width, panel_height), 2, border_radius=12)

        # Header
        header_height = 50
        pygame.draw.rect(self.screen, self.ACCENT_BLUE, (panel_x,
                         panel_y, panel_width, header_height), border_radius=12)
        pygame.draw.rect(self.screen, self.ACCENT_BLUE,
                         (panel_x, panel_y + header_height - 12, panel_width, 12))

        title_text = self.subtitle_font.render(
            "Performance Stats", True, self.CARD_BG)
        title_rect = title_text.get_rect(
            center=(panel_x + panel_width // 2, panel_y + header_height // 2))
        self.screen.blit(title_text, title_rect)

        # Content area
        content_x = panel_x + 20
        content_y = panel_y + header_height + 20
        stats = self.recent_performance_stats

        # Games analyzed
        games_text = self.menu_font.render(
            f"Games Analyzed: {stats['games_count']}", True, self.TEXT_PRIMARY)
        self.screen.blit(games_text, (content_x, content_y))
        content_y += 30

        # Average score with modern progress bar
        avg_text = self.menu_font.render(
            "Average Score", True, self.TEXT_PRIMARY)
        self.screen.blit(avg_text, (content_x, content_y))

        score_text = self.menu_font.render(
            f"{stats['average_score']:.1f}%", True, self.ACCENT_BLUE)
        score_rect = score_text.get_rect(right=panel_x + panel_width - 20)
        score_rect.y = content_y
        self.screen.blit(score_text, score_rect)

        content_y += 25
        self.draw_modern_difficulty_bar(
            content_x, content_y, stats['average_score'], panel_width - 40, 8)
        content_y += 25

        # Win rate
        win_text = self.menu_font.render("Win Rate", True, self.TEXT_PRIMARY)
        self.screen.blit(win_text, (content_x, content_y))

        win_rate_text = self.menu_font.render(
            f"{stats['win_rate']:.1f}%", True, self.ACCENT_GREEN)
        win_rate_rect = win_rate_text.get_rect(
            right=panel_x + panel_width - 20)
        win_rate_rect.y = content_y
        self.screen.blit(win_rate_text, win_rate_rect)
        content_y += 25

        self.draw_modern_difficulty_bar(
            content_x, content_y, stats['win_rate'], panel_width - 40, 8)
        content_y += 35

        # Trend with icon
        trend_text = self.menu_font.render("Trend:", True, self.TEXT_PRIMARY)
        self.screen.blit(trend_text, (content_x, content_y))

        trend_color = self.ACCENT_GREEN if stats['trend'] == 'improving' else (
            220, 53, 69) if stats['trend'] == 'declining' else self.TEXT_SECONDARY
        trend_icon = "↗" if stats['trend'] == 'improving' else "↘" if stats['trend'] == 'declining' else "→"
        trend_value = self.menu_font.render(
            f"{trend_icon} {stats['trend'].title()}", True, trend_color)
        trend_rect = trend_value.get_rect(right=panel_x + panel_width - 20)
        trend_rect.y = content_y
        self.screen.blit(trend_value, trend_rect)
        content_y += 40

        # Recent games section
        games_header = self.menu_font.render(
            "Recent Games", True, self.TEXT_PRIMARY)
        self.screen.blit(games_header, (content_x, content_y))
        content_y += 25

        # Game history list
        # Show up to 4 games
        for i, game in enumerate(stats['performance_history'][:4]):
            if content_y >= panel_y + panel_height - 30:
                break

            game_num = game['game_number']
            score = game['score']
            won = game['won']

            # Game item background
            item_height = 22
            item_bg_color = self.HOVER_BG if i % 2 == 0 else self.CARD_BG
            pygame.draw.rect(self.screen, item_bg_color, (content_x - 5,
                             content_y - 2, panel_width - 30, item_height))

            # Game info
            game_text = self.info_font.render(
                f"Game {game_num}", True, self.TEXT_PRIMARY)
            self.screen.blit(game_text, (content_x, content_y))

            score_text = self.info_font.render(
                f"{score:.0f}%", True, self.TEXT_SECONDARY)
            self.screen.blit(score_text, (content_x + 80, content_y))

            status_color = self.ACCENT_GREEN if won else (220, 53, 69)
            status_text = self.info_font.render(
                "Won" if won else "Lost", True, status_color)
            status_rect = status_text.get_rect(
                right=panel_x + panel_width - 25)
            status_rect.y = content_y
            self.screen.blit(status_text, status_rect)

            content_y += item_height + 2

    def draw_no_performance_data_message(self):
        """Draw a message when no performance data is available"""
        # Panel dimensions and positioning (same as performance panel)
        panel_width = 380
        panel_height = 200
        panel_x = self.SCREEN_WIDTH - panel_width - 30
        panel_y = 140

        # Panel background with shadow effect
        shadow_offset = 4
        pygame.draw.rect(self.screen, (0, 0, 0, 30), (panel_x + shadow_offset,
                         panel_y + shadow_offset, panel_width, panel_height), border_radius=12)
        pygame.draw.rect(self.screen, self.CARD_BG, (panel_x,
                         panel_y, panel_width, panel_height), border_radius=12)
        pygame.draw.rect(self.screen, self.BORDER_LIGHT, (panel_x,
                         panel_y, panel_width, panel_height), 2, border_radius=12)

        # Header
        header_height = 50
        pygame.draw.rect(self.screen, self.ACCENT_ORANGE, (panel_x,
                         panel_y, panel_width, header_height), border_radius=12)
        pygame.draw.rect(self.screen, self.ACCENT_ORANGE,
                         (panel_x, panel_y + header_height - 12, panel_width, 12))

        title_text = self.subtitle_font.render(
            "No Performance Data", True, self.CARD_BG)
        title_rect = title_text.get_rect(
            center=(panel_x + panel_width // 2, panel_y + header_height // 2))
        self.screen.blit(title_text, title_rect)

        # Content area
        content_x = panel_x + 20
        content_y = panel_y + header_height + 30

        # Message
        message_lines = [
            "No game data found.",
            "",
            "Play some games first to",
            "generate performance",
            "statistics and enable",
            "adaptive AI features."
        ]

        for i, line in enumerate(message_lines):
            if line:  # Skip empty lines
                message_text = self.menu_font.render(
                    line, True, self.TEXT_PRIMARY)
                self.screen.blit(message_text, (content_x, content_y + i * 20))

    def run(self):
        """Main launcher loop"""
        while self.running:
            self.handle_events()
            self.draw()
            self.clock.tick(self.FPS)

            # Clear status message after 5 seconds
            if self.show_generation_status:
                if not hasattr(self, '_status_timer'):
                    self._status_timer = time.time()
                elif time.time() - self._status_timer > 5:
                    self.show_generation_status = False
                    delattr(self, '_status_timer')

        pygame.quit()
        sys.exit()


def main():
    """Main entry point"""
    print("=== Tower Defense Game Launcher ===")
    launcher = GameLauncher()
    launcher.run()


if __name__ == "__main__":
    main()
